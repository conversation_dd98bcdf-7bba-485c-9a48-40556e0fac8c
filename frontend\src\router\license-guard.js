// 授权路由守卫 - 强制授权验证
import { enhancedLicenseManager, requiresAuthorization } from '@/utils/enhanced-license.js'

// 需要授权的路由列表
const PROTECTED_ROUTES = [
  'devices',
  'backups', 
  'commands',
  'comparison',
  'settings'
]

// 不需要授权的公开路由
const PUBLIC_ROUTES = [
  'license-gate'
]

// 授权验证守卫
export function createLicenseGuard() {
  return async (to, from, next) => {
    try {
      // 检查是否为公开路由
      if (PUBLIC_ROUTES.includes(to.name)) {
        next()
        return
      }

      // 检查是否需要授权
      if (requiresAuthorization()) {
        console.log('🔒 未授权访问，重定向到授权页面')
        
        // 保存原始目标路由
        sessionStorage.setItem('license_redirect_route', JSON.stringify({
          name: to.name,
          params: to.params,
          query: to.query,
          path: to.path
        }))

        // 重定向到授权页面
        next({
          name: 'license-gate',
          query: { 
            redirect: to.fullPath,
            reason: 'unauthorized'
          }
        })
        return
      }

      // 对于受保护的路由，进行服务器验证
      if (PROTECTED_ROUTES.includes(to.name)) {
        console.log('🔍 验证受保护路由的授权状态')
        
        const isValid = await enhancedLicenseManager.verifyWithServer()
        if (!isValid) {
          console.log('🚫 服务器授权验证失败')
          
          // 保存原始目标路由
          sessionStorage.setItem('license_redirect_route', JSON.stringify({
            name: to.name,
            params: to.params,
            query: to.query,
            path: to.path
          }))

          // 重定向到授权页面
          next({
            name: 'license-gate',
            query: { 
              redirect: to.fullPath,
              reason: 'verification_failed'
            }
          })
          return
        }
      }

      // 授权验证通过，允许访问
      next()
      
    } catch (error) {
      console.error('路由守卫执行失败:', error)
      
      // 发生错误时重定向到授权页面
      next({
        name: 'license-gate',
        query: { 
          redirect: to.fullPath,
          reason: 'guard_error'
        }
      })
    }
  }
}

// 获取重定向路由
export function getRedirectRoute() {
  try {
    const savedRoute = sessionStorage.getItem('license_redirect_route')
    if (savedRoute) {
      sessionStorage.removeItem('license_redirect_route')
      return JSON.parse(savedRoute)
    }
  } catch (error) {
    console.error('获取重定向路由失败:', error)
  }
  
  // 默认重定向到设备管理页面
  return { name: 'devices' }
}

// 清除重定向路由
export function clearRedirectRoute() {
  sessionStorage.removeItem('license_redirect_route')
}

// 检查路由是否需要授权
export function isProtectedRoute(routeName) {
  return PROTECTED_ROUTES.includes(routeName)
}

// 检查路由是否为公开路由
export function isPublicRoute(routeName) {
  return PUBLIC_ROUTES.includes(routeName)
}

// 授权状态监听器
export function setupLicenseListeners(router) {
  // 监听授权验证失败事件
  window.addEventListener('license-verification-failed', (event) => {
    console.log('🚨 授权验证失败事件:', event.detail)
    
    // 保存当前路由
    const currentRoute = router.currentRoute.value
    if (isProtectedRoute(currentRoute.name)) {
      sessionStorage.setItem('license_redirect_route', JSON.stringify({
        name: currentRoute.name,
        params: currentRoute.params,
        query: currentRoute.query,
        path: currentRoute.path
      }))
    }

    // 重定向到授权页面
    router.push({
      name: 'license-gate',
      query: { 
        redirect: currentRoute.fullPath,
        reason: event.detail.reason || 'verification_failed'
      }
    })
  })

  // 监听授权成功事件
  window.addEventListener('license-activated', async (event) => {
    console.log('✅ 授权激活成功事件:', event.detail)

    try {
      // 获取重定向路由
      const redirectRoute = getRedirectRoute()
      console.log('🎯 准备重定向到:', redirectRoute)

      // 添加短暂延迟确保UI状态更新完成
      setTimeout(async () => {
        try {
          // 重定向到目标页面
          await router.push(redirectRoute)
          console.log('🚀 重定向成功完成')
        } catch (redirectError) {
          console.error('❌ 重定向失败:', redirectError)
          // 如果重定向失败，尝试重定向到默认页面
          await router.push({ name: 'devices' })
        }
      }, 500) // 500ms延迟确保状态显示

    } catch (error) {
      console.error('💥 处理授权成功事件时发生错误:', error)
      // 发生错误时也尝试重定向到设备管理页面
      setTimeout(() => {
        router.push({ name: 'devices' })
      }, 500)
    }
  })

  // 监听页面可见性变化，重新验证授权
  document.addEventListener('visibilitychange', async () => {
    if (!document.hidden && enhancedLicenseManager.hasValidLicense()) {
      console.log('🔄 页面重新可见，验证授权状态')
      
      const isValid = await enhancedLicenseManager.verifyWithServer()
      if (!isValid) {
        window.dispatchEvent(new CustomEvent('license-verification-failed', {
          detail: { reason: 'visibility_check_failed' }
        }))
      }
    }
  })
}

// 强制重新授权
export function forceReauthorization(reason = 'manual') {
  console.log('🔄 强制重新授权:', reason)
  
  // 清除当前授权
  enhancedLicenseManager.clearLicense()
  
  // 触发重新授权事件
  window.dispatchEvent(new CustomEvent('license-verification-failed', {
    detail: { reason: reason }
  }))
}

// 授权状态检查工具
export const LicenseGuardUtils = {
  // 检查当前是否有有效授权
  hasValidLicense: () => enhancedLicenseManager.hasValidLicense(),
  
  // 检查是否需要授权
  requiresAuth: () => requiresAuthorization(),
  
  // 获取授权信息
  getLicenseInfo: () => enhancedLicenseManager.getLicenseInfo(),
  
  // 验证特定功能权限
  checkFeatureAccess: (feature) => enhancedLicenseManager.hasFeature(feature),
  
  // 获取错误信息
  getErrorMessage: (errorCode) => {
    const errorMessages = {
      'EMPTY_CODE': '授权码不能为空',
      'INVALID_CODE': '无效的授权码',
      'DEVICE_MISMATCH': '此授权码已在其他设备上激活',
      'LICENSE_EXPIRED': '授权已过期',
      'LICENSE_NOT_FOUND': '授权码不存在或已失效',
      'INTERNAL_ERROR': '系统内部错误',
      'NETWORK_ERROR': '网络连接失败'
    }
    return errorMessages[errorCode] || '未知错误'
  },
  
  // 格式化授权类型
  formatLicenseType: (type) => {
    const typeMap = {
      'basic': '基础版',
      'professional': '专业版'
    }
    return typeMap[type] || '未知版本'
  }
}

export default {
  createLicenseGuard,
  getRedirectRoute,
  clearRedirectRoute,
  isProtectedRoute,
  isPublicRoute,
  setupLicenseListeners,
  forceReauthorization,
  LicenseGuardUtils
}
