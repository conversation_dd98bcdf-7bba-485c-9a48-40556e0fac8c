// 增强的授权管理模块 - 强制授权验证系统
import CryptoJS from 'crypto-js'
import axios from 'axios'

// 版本类型
export const LICENSE_TYPES = {
  BASIC: 'basic',
  PROFESSIONAL: 'professional'
}

// 功能权限定义
export const FEATURES = {
  DEVICE_BATCH_BACKUP: 'device_batch_backup',
  DEVICE_BATCH_INSPECTION: 'device_batch_inspection',
  DEVICE_BATCH_DELETE: 'device_batch_delete',
  DEVICE_IMPORT_EXPORT: 'device_import_export',
  DEVICE_AUTO_BACKUP: 'device_auto_backup',
  CUSTOM_COMMANDS: 'custom_commands',
  CONFIG_COMPARISON: 'config_comparison',
  ADVANCED_SETTINGS: 'advanced_settings',
  ADVANCED_REPORTS: 'advanced_reports',
  PERFORMANCE_ANALYSIS: 'performance_analysis',
}

// 版本功能映射
const VERSION_FEATURES = {
  [LICENSE_TYPES.BASIC]: [],
  [LICENSE_TYPES.PROFESSIONAL]: [
    FEATURES.DEVICE_BATCH_BACKUP,
    FEATURES.DEVICE_BATCH_INSPECTION,
    FEATURES.DEVICE_BATCH_DELETE,
    FEATURES.DEVICE_IMPORT_EXPORT,
    FEATURES.DEVICE_AUTO_BACKUP,
    FEATURES.CUSTOM_COMMANDS,
    FEATURES.CONFIG_COMPARISON,
    FEATURES.ADVANCED_SETTINGS,
    FEATURES.ADVANCED_REPORTS,
    FEATURES.PERFORMANCE_ANALYSIS,
  ]
}

// 存储密钥和配置
const STORAGE_KEY = 'net_tools_license_v2'
const ENCRYPTION_KEY = 'NetTools2024SecretKey_Enhanced'
const API_BASE_URL = '/api'  // 使用Vite代理，自动转发到正确的后端端口
const VERIFICATION_INTERVAL = 5 * 60 * 1000 // 5分钟验证一次

// 错误代码定义
export const ERROR_CODES = {
  EMPTY_CODE: 'EMPTY_CODE',
  INVALID_CODE: 'INVALID_CODE',
  DEVICE_MISMATCH: 'DEVICE_MISMATCH',
  LICENSE_EXPIRED: 'LICENSE_EXPIRED',
  LICENSE_NOT_FOUND: 'LICENSE_NOT_FOUND',
  INTERNAL_ERROR: 'INTERNAL_ERROR',
  NETWORK_ERROR: 'NETWORK_ERROR'
}

// 生成增强的设备指纹
function generateEnhancedDeviceFingerprint() {
  const canvas = document.createElement('canvas')
  const ctx = canvas.getContext('2d')
  ctx.textBaseline = 'top'
  ctx.font = '14px Arial'
  ctx.fillText('Enhanced Device Fingerprint', 2, 2)
  
  const fingerprint = [
    navigator.userAgent,
    navigator.language,
    navigator.languages?.join(',') || '',
    screen.width + 'x' + screen.height,
    screen.colorDepth,
    new Date().getTimezoneOffset(),
    navigator.hardwareConcurrency || 0,
    navigator.deviceMemory || 0,
    canvas.toDataURL(),
    window.location.hostname,
    // 添加时间戳防止简单破解
    Math.floor(Date.now() / (1000 * 60 * 60 * 24)) // 按天变化
  ].join('|')
  
  return CryptoJS.SHA256(fingerprint).toString()
}

// 生成验证令牌
function generateVerificationToken(licenseCode, deviceFingerprint) {
  const timestamp = Date.now()
  const data = `${licenseCode}:${deviceFingerprint}:${timestamp}`
  return CryptoJS.HmacSHA256(data, ENCRYPTION_KEY).toString()
}

// 验证令牌
function verifyToken(token, licenseCode, deviceFingerprint, maxAge = 300000) { // 5分钟有效期
  try {
    const timestamp = Date.now()
    const data = `${licenseCode}:${deviceFingerprint}:${timestamp}`
    const expectedToken = CryptoJS.HmacSHA256(data, ENCRYPTION_KEY).toString()
    
    // 简单的时间窗口验证
    return token === expectedToken
  } catch (error) {
    return false
  }
}

// 增强的授权管理类
class EnhancedLicenseManager {
  constructor() {
    this.currentLicense = null
    this.verificationTimer = null
    this.isVerifying = false
    this.deviceFingerprint = generateEnhancedDeviceFingerprint()
    this.initializeManager()
  }

  async initializeManager() {
    try {
      // 加载本地授权信息
      this.currentLicense = this.loadLicense()
      
      // 如果有授权信息，立即验证
      if (this.currentLicense) {
        await this.verifyWithServer()
      }
      
      // 启动定期验证
      this.startPeriodicVerification()
    } catch (error) {
      console.error('授权管理器初始化失败:', error)
      this.clearLicense()
    }
  }

  // 强制授权检查
  requiresAuthorization() {
    return !this.hasValidLicense()
  }

  // 检查是否有有效授权
  hasValidLicense() {
    if (!this.currentLicense) {
      return false
    }

    // 检查授权是否过期
    if (this.currentLicense.expiresAt && new Date(this.currentLicense.expiresAt) < new Date()) {
      this.clearLicense()
      return false
    }

    // 检查设备指纹
    if (this.currentLicense.deviceFingerprint !== this.deviceFingerprint) {
      this.clearLicense()
      return false
    }

    return true
  }

  // 获取当前授权类型
  getCurrentLicenseType() {
    return this.currentLicense?.type || null
  }

  // 检查是否为基础版
  isBasic() {
    return this.getCurrentLicenseType() === LICENSE_TYPES.BASIC
  }

  // 检查是否为专业版
  isProfessional() {
    return this.getCurrentLicenseType() === LICENSE_TYPES.PROFESSIONAL
  }

  // 通知授权状态变化
  notifyLicenseStateChange() {
    // 触发自定义事件，通知Vue组件授权状态已变化
    if (typeof window !== 'undefined') {
      window.dispatchEvent(new CustomEvent('license-state-changed', {
        detail: {
          hasLicense: this.hasValidLicense(),
          licenseType: this.getCurrentLicenseType(),
          isBasic: this.isBasic(),
          isProfessional: this.isProfessional()
        }
      }))
    }
  }

  // 检查功能权限
  hasFeature(feature) {
    if (!this.hasValidLicense()) {
      return false
    }
    const currentType = this.getCurrentLicenseType()
    const features = VERSION_FEATURES[currentType] || []
    return features.includes(feature)
  }

  // 获取所有可用功能
  getAvailableFeatures() {
    if (!this.hasValidLicense()) {
      return []
    }
    const currentType = this.getCurrentLicenseType()
    return VERSION_FEATURES[currentType] || []
  }

  // 激活授权（与服务器通信）
  async activateLicense(licenseCode) {
    try {
      this.isVerifying = true



      const response = await axios.post(`${API_BASE_URL}/license/activate`, {
        license_code: licenseCode,
        device_fingerprint: this.deviceFingerprint,
        client_info: {
          userAgent: navigator.userAgent,
          timestamp: Date.now(),
          url: window.location.href
        }
      })

      if (response.data.success) {
        const licenseData = {
          code: licenseCode.trim().toUpperCase(),
          type: response.data.license_type,
          activatedAt: new Date().toISOString(),
          deviceFingerprint: this.deviceFingerprint,
          features: response.data.features || [],
          expiresAt: response.data.expires_at,
          lastVerified: new Date().toISOString(),
          verificationToken: generateVerificationToken(licenseCode, this.deviceFingerprint)
        }

        this.saveLicense(licenseData)
        this.currentLicense = licenseData

        // 启动定期验证
        this.startPeriodicVerification()

        // 触发授权状态变化事件，通知Vue组件更新
        this.notifyLicenseStateChange()

        return {
          success: true,
          type: response.data.license_type,
          message: response.data.message
        }
      } else {
        return {
          success: false,
          error: response.data.message,
          errorCode: response.data.error_code
        }
      }
    } catch (error) {
      console.error('授权激活失败:', error)
      return {
        success: false,
        error: '网络连接失败，请检查网络连接',
        errorCode: ERROR_CODES.NETWORK_ERROR
      }
    } finally {
      this.isVerifying = false
    }
  }

  // 与服务器验证授权
  async verifyWithServer() {
    if (!this.currentLicense || this.isVerifying) {
      return false
    }

    try {
      this.isVerifying = true



      const response = await axios.post(`${API_BASE_URL}/license/verify`, {
        license_code: this.currentLicense.code,
        device_fingerprint: this.deviceFingerprint,
        client_info: {
          userAgent: navigator.userAgent,
          timestamp: Date.now(),
          url: window.location.href
        }
      })

      if (response.data.success) {
        // 更新本地授权信息
        this.currentLicense.lastVerified = new Date().toISOString()
        this.currentLicense.features = response.data.features || []
        this.saveLicense(this.currentLicense)
        return true
      } else {
        // 验证失败，清除本地授权
        console.warn('服务器授权验证失败:', response.data.message)
        this.clearLicense()
        return false
      }
    } catch (error) {
      console.error('服务器授权验证失败:', error)
      // 网络错误时不清除本地授权，但标记为需要重新验证
      return false
    } finally {
      this.isVerifying = false
    }
  }

  // 启动定期验证
  startPeriodicVerification() {
    if (this.verificationTimer) {
      clearInterval(this.verificationTimer)
    }

    this.verificationTimer = setInterval(async () => {
      if (this.currentLicense) {
        const isValid = await this.verifyWithServer()
        if (!isValid) {
          // 触发重新授权事件
          window.dispatchEvent(new CustomEvent('license-verification-failed', {
            detail: { reason: 'periodic_verification_failed' }
          }))
        }
      }
    }, VERIFICATION_INTERVAL)
  }

  // 停止定期验证
  stopPeriodicVerification() {
    if (this.verificationTimer) {
      clearInterval(this.verificationTimer)
      this.verificationTimer = null
    }
  }

  // 保存授权信息（加密存储）
  saveLicense(licenseData) {
    try {
      const encrypted = CryptoJS.AES.encrypt(
        JSON.stringify(licenseData), 
        ENCRYPTION_KEY
      ).toString()
      localStorage.setItem(STORAGE_KEY, encrypted)
    } catch (error) {
      console.error('保存授权信息失败:', error)
    }
  }

  // 加载授权信息（解密）
  loadLicense() {
    try {
      const encrypted = localStorage.getItem(STORAGE_KEY)
      if (!encrypted) return null

      const decrypted = CryptoJS.AES.decrypt(encrypted, ENCRYPTION_KEY)
      const licenseData = JSON.parse(decrypted.toString(CryptoJS.enc.Utf8))

      // 验证设备指纹
      if (licenseData.deviceFingerprint !== this.deviceFingerprint) {
        console.warn('设备指纹不匹配，清除授权信息')
        this.clearLicense()
        return null
      }

      return licenseData
    } catch (error) {
      console.error('加载授权信息失败:', error)
      this.clearLicense()
      return null
    }
  }

  // 清除授权信息
  clearLicense() {
    localStorage.removeItem(STORAGE_KEY)
    this.currentLicense = null
    this.stopPeriodicVerification()
  }

  // 重置为基础版
  resetToBasic() {
    this.clearLicense()
  }

  // 获取授权信息
  getLicenseInfo() {
    return {
      type: this.getCurrentLicenseType(),
      hasAnyLicense: this.hasValidLicense(),
      isBasic: this.isBasic(),
      isProfessional: this.isProfessional(),
      features: this.getAvailableFeatures(),
      activatedAt: this.currentLicense?.activatedAt,
      code: this.currentLicense?.code,
      expiresAt: this.currentLicense?.expiresAt,
      lastVerified: this.currentLicense?.lastVerified,
      deviceFingerprint: this.deviceFingerprint
    }
  }

  // 销毁管理器
  destroy() {
    this.stopPeriodicVerification()
    this.currentLicense = null
  }
}

// 创建全局实例
export const enhancedLicenseManager = new EnhancedLicenseManager()

// 便捷函数
export const hasFeature = (feature) => enhancedLicenseManager.hasFeature(feature)
export const hasAnyLicense = () => enhancedLicenseManager.hasValidLicense()
export const isBasic = () => enhancedLicenseManager.isBasic()
export const isProfessional = () => enhancedLicenseManager.isProfessional()
export const getCurrentLicenseType = () => enhancedLicenseManager.getCurrentLicenseType()
export const getLicenseInfo = () => enhancedLicenseManager.getLicenseInfo()
export const requiresAuthorization = () => enhancedLicenseManager.requiresAuthorization()

// 功能描述映射
export const FEATURE_DESCRIPTIONS = {
  [FEATURES.DEVICE_BATCH_BACKUP]: '批量设备备份',
  [FEATURES.DEVICE_BATCH_INSPECTION]: '批量设备巡检',
  [FEATURES.DEVICE_BATCH_DELETE]: '批量设备删除',
  [FEATURES.DEVICE_IMPORT_EXPORT]: '设备导入导出',
  [FEATURES.DEVICE_AUTO_BACKUP]: '自动备份调度',
  [FEATURES.CUSTOM_COMMANDS]: '自定义命令执行',
  [FEATURES.CONFIG_COMPARISON]: '配置文件对比',
  [FEATURES.ADVANCED_SETTINGS]: '高级系统设置',
  [FEATURES.ADVANCED_REPORTS]: '高级报告生成',
  [FEATURES.PERFORMANCE_ANALYSIS]: '性能分析统计',
}

export default enhancedLicenseManager
