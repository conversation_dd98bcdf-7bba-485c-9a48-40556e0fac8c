// 授权管理模块
import CryptoJS from 'crypto-js'
import { generateBatchLicenseCodes } from './batch-license-codes.js'

// 版本类型
export const LICENSE_TYPES = {
  BASIC: 'basic',
  PROFESSIONAL: 'professional'
}

// 功能权限定义
export const FEATURES = {
  // 设备管理功能
  DEVICE_BATCH_BACKUP: 'device_batch_backup',           // 批量备份
  DEVICE_BATCH_INSPECTION: 'device_batch_inspection',   // 批量巡检
  DEVICE_BATCH_DELETE: 'device_batch_delete',           // 批量删除
  DEVICE_IMPORT_EXPORT: 'device_import_export',         // 导入导出
  DEVICE_AUTO_BACKUP: 'device_auto_backup',             // 自动备份
  
  // 高级功能
  CUSTOM_COMMANDS: 'custom_commands',                    // 自定义命令
  CONFIG_COMPARISON: 'config_comparison',               // 配置对比
  ADVANCED_SETTINGS: 'advanced_settings',              // 高级设置
  
  // 报告和分析
  ADVANCED_REPORTS: 'advanced_reports',                // 高级报告
  PERFORMANCE_ANALYSIS: 'performance_analysis',        // 性能分析
}

// 版本功能映射
const VERSION_FEATURES = {
  [LICENSE_TYPES.BASIC]: [
    // 基础版只包含基本的设备管理功能
    // 不包含任何高级功能
  ],
  [LICENSE_TYPES.PROFESSIONAL]: [
    // 专业版包含所有功能
    FEATURES.DEVICE_BATCH_BACKUP,
    FEATURES.DEVICE_BATCH_INSPECTION,
    FEATURES.DEVICE_BATCH_DELETE,
    FEATURES.DEVICE_IMPORT_EXPORT,
    FEATURES.DEVICE_AUTO_BACKUP,
    FEATURES.CUSTOM_COMMANDS,
    FEATURES.CONFIG_COMPARISON,
    FEATURES.ADVANCED_SETTINGS,
    FEATURES.ADVANCED_REPORTS,
    FEATURES.PERFORMANCE_ANALYSIS,
  ]
}

// 存储密钥
const STORAGE_KEY = 'net_tools_license'
const ENCRYPTION_KEY = 'NetTools2024SecretKey'

// 生成设备指纹
function generateDeviceFingerprint() {
  const canvas = document.createElement('canvas')
  const ctx = canvas.getContext('2d')
  ctx.textBaseline = 'top'
  ctx.font = '14px Arial'
  ctx.fillText('Device fingerprint', 2, 2)
  
  const fingerprint = [
    navigator.userAgent,
    navigator.language,
    screen.width + 'x' + screen.height,
    new Date().getTimezoneOffset(),
    canvas.toDataURL()
  ].join('|')
  
  return CryptoJS.MD5(fingerprint).toString()
}

// 预设的有效授权码（生产环境中应该从服务器获取）
const VALID_LICENSE_CODES = {
  // 生产环境中的授权码应该从后端API获取
  // 这里只保留空对象作为示例结构
}

// 生成批量授权码
const BATCH_LICENSE_CODES = generateBatchLicenseCodes(LICENSE_TYPES, VERSION_FEATURES)

// 合并所有有效授权码（包括预设的和批量生成的）
const ALL_VALID_LICENSE_CODES = {
  ...VALID_LICENSE_CODES,
  ...BATCH_LICENSE_CODES
}

// 授权管理类
class LicenseManager {
  constructor() {
    this.currentLicense = this.loadLicense()
  }

  // 获取当前授权类型
  getCurrentLicenseType() {
    return this.currentLicense?.type || null
  }

  // 检查是否有任何授权
  hasAnyLicense() {
    return this.currentLicense !== null && this.currentLicense !== undefined
  }

  // 检查是否为基础版
  isBasic() {
    return this.getCurrentLicenseType() === LICENSE_TYPES.BASIC
  }

  // 检查是否为专业版
  isProfessional() {
    return this.getCurrentLicenseType() === LICENSE_TYPES.PROFESSIONAL
  }

  // 检查功能权限
  hasFeature(feature) {
    if (!this.hasAnyLicense()) {
      return false
    }
    const currentType = this.getCurrentLicenseType()
    const features = VERSION_FEATURES[currentType] || []
    return features.includes(feature)
  }

  // 获取所有可用功能
  getAvailableFeatures() {
    if (!this.hasAnyLicense()) {
      return []
    }
    const currentType = this.getCurrentLicenseType()
    return VERSION_FEATURES[currentType] || []
  }

  // 验证授权码
  validateLicenseCode(code) {
    if (!code || typeof code !== 'string') {
      return { valid: false, error: '授权码不能为空' }
    }

    const normalizedCode = code.trim().toUpperCase()

    if (ALL_VALID_LICENSE_CODES[normalizedCode]) {
      const licenseInfo = ALL_VALID_LICENSE_CODES[normalizedCode]
      return {
        valid: true,
        type: licenseInfo.type,
        description: licenseInfo.description,
        features: licenseInfo.features
      }
    }

    return { valid: false, error: '无效的授权码' }
  }

  // 激活授权
  activateLicense(code) {
    const validation = this.validateLicenseCode(code)
    
    if (!validation.valid) {
      return validation
    }

    const deviceFingerprint = generateDeviceFingerprint()
    const licenseData = {
      code: code.trim().toUpperCase(),
      type: validation.type,
      activatedAt: new Date().toISOString(),
      deviceFingerprint: deviceFingerprint,
      features: validation.features
    }

    this.saveLicense(licenseData)
    this.currentLicense = licenseData

    return {
      success: true,
      type: validation.type,
      message: '授权激活成功！'
    }
  }

  // 重置为基础版
  resetToBasic() {
    this.clearLicense()
    this.currentLicense = null
  }

  // 保存授权信息
  saveLicense(licenseData) {
    try {
      const encrypted = CryptoJS.AES.encrypt(
        JSON.stringify(licenseData), 
        ENCRYPTION_KEY
      ).toString()
      localStorage.setItem(STORAGE_KEY, encrypted)
    } catch (error) {
      console.error('保存授权信息失败:', error)
    }
  }

  // 加载授权信息
  loadLicense() {
    try {
      const encrypted = localStorage.getItem(STORAGE_KEY)
      if (!encrypted) return null

      const decrypted = CryptoJS.AES.decrypt(encrypted, ENCRYPTION_KEY)
      const licenseData = JSON.parse(decrypted.toString(CryptoJS.enc.Utf8))

      // 验证设备指纹
      const currentFingerprint = generateDeviceFingerprint()
      if (licenseData.deviceFingerprint !== currentFingerprint) {
        console.warn('设备指纹不匹配，清除授权信息')
        this.clearLicense()
        return null
      }

      return licenseData
    } catch (error) {
      console.error('加载授权信息失败:', error)
      this.clearLicense()
      return null
    }
  }

  // 清除授权信息
  clearLicense() {
    localStorage.removeItem(STORAGE_KEY)
  }

  // 获取授权信息
  getLicenseInfo() {
    return {
      type: this.getCurrentLicenseType(),
      hasAnyLicense: this.hasAnyLicense(),
      isBasic: this.isBasic(),
      isProfessional: this.isProfessional(),
      features: this.getAvailableFeatures(),
      activatedAt: this.currentLicense?.activatedAt,
      code: this.currentLicense?.code
    }
  }
}

// 创建全局实例
export const licenseManager = new LicenseManager()

// 便捷函数
export const hasFeature = (feature) => licenseManager.hasFeature(feature)
export const hasAnyLicense = () => licenseManager.hasAnyLicense()
export const isBasic = () => licenseManager.isBasic()
export const isProfessional = () => licenseManager.isProfessional()
export const getCurrentLicenseType = () => licenseManager.getCurrentLicenseType()
export const getLicenseInfo = () => licenseManager.getLicenseInfo()

// 功能描述映射
export const FEATURE_DESCRIPTIONS = {
  [FEATURES.DEVICE_BATCH_BACKUP]: '批量设备备份',
  [FEATURES.DEVICE_BATCH_INSPECTION]: '批量设备巡检',
  [FEATURES.DEVICE_BATCH_DELETE]: '批量设备删除',
  [FEATURES.DEVICE_IMPORT_EXPORT]: '设备导入导出',
  [FEATURES.DEVICE_AUTO_BACKUP]: '自动备份调度',
  [FEATURES.CUSTOM_COMMANDS]: '自定义命令执行',
  [FEATURES.CONFIG_COMPARISON]: '配置文件对比',
  [FEATURES.ADVANCED_SETTINGS]: '高级系统设置',
  [FEATURES.ADVANCED_REPORTS]: '高级报告生成',
  [FEATURES.PERFORMANCE_ANALYSIS]: '性能分析统计',
}

export default licenseManager
