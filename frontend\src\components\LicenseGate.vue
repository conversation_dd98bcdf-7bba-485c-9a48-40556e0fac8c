<template>
  <div class="license-gate">
    <div class="gate-container">
      <!-- 系统标题 -->
      <div class="system-header">
        <div class="system-logo">
          <div class="logo-icon">🔧</div>
          <h1>网络设备管理系统</h1>
        </div>
        <div class="system-subtitle">
          专业的网络设备备份与巡检工具
        </div>
      </div>

      <!-- 授权输入区域 -->
      <div class="license-input-section">
        <div class="input-header">
          <h2 v-if="props.mode === 'initial'">🔐 系统授权验证</h2>
          <h2 v-else-if="props.mode === 'upgrade'">⬆️ 升级到专业版</h2>
          <h2 v-else-if="props.mode === 'reauth'">🔄 重新授权</h2>

          <p v-if="props.mode === 'initial'">请输入您的授权码以使用本系统</p>
          <p v-else-if="props.mode === 'upgrade'">请输入专业版授权码以升级您的账户</p>
          <p v-else-if="props.mode === 'reauth'">请输入新的授权码以更换当前授权</p>

          <!-- 当前授权状态显示（重新授权模式） -->
          <div v-if="props.mode === 'reauth' && props.currentLicenseInfo" class="current-license-info">
            <div class="current-status">
              <h4>📊 当前授权状态</h4>
              <div class="status-details">
                <span class="status-item">
                  <strong>版本：</strong>{{ props.currentLicenseInfo.isProfessional ? '专业版' : '基础版' }}
                </span>
                <span class="status-item">
                  <strong>授权码：</strong>{{ props.currentLicenseInfo.code }}
                </span>
                <span class="status-item">
                  <strong>激活时间：</strong>{{ props.currentLicenseInfo.activatedAt ? new Date(props.currentLicenseInfo.activatedAt).toLocaleString() : '未知' }}
                </span>
              </div>
            </div>
          </div>
        </div>

        <div class="license-form">
          <div class="input-group">
            <label for="licenseCode">授权码</label>
            <input
              id="licenseCode"
              v-model="licenseCode"
              type="text"
              placeholder="请输入授权码"
              class="license-input"
              @keyup.enter="activateLicense"
              @input="clearError"
            />
          </div>

          <div class="button-group">
            <button
              class="activate-btn"
              @click="activateLicense"
              :disabled="!licenseCode.trim() || activating"
            >
              <span v-if="activating">验证中...</span>
              <span v-else-if="props.mode === 'upgrade'">升级授权</span>
              <span v-else-if="props.mode === 'reauth'">更换授权</span>
              <span v-else>激活授权</span>
            </button>

            <button
              v-if="props.mode !== 'initial' && props.onCancel"
              class="cancel-btn"
              @click="handleCancel"
              :disabled="activating"
            >
              取消
            </button>
          </div>

          <!-- 错误提示 -->
          <div v-if="errorMessage" class="error-message">
            <span class="error-icon">⚠️</span>
            {{ errorMessage }}
          </div>
        </div>
      </div>

      <!-- 授权码说明 -->
      <div class="license-info">
        <div class="info-section">
          <h3>📋 授权码类型</h3>
          <div class="license-types">
            <div class="license-type basic">
              <div class="type-header">
                <span class="type-badge basic">基础版</span>
                <span class="type-price">免费试用</span>
              </div>
              <div class="type-features">
                <div class="feature">✓ 设备管理</div>
                <div class="feature">✓ 单个设备备份</div>
                <div class="feature">✓ 单个设备巡检</div>
                <div class="feature">✓ 状态监控</div>
              </div>
            </div>
            
            <div class="license-type professional">
              <div class="type-header">
                <span class="type-badge professional">专业版</span>
                <span class="type-price">完整功能</span>
              </div>
              <div class="type-features">
                <div class="feature">✓ 基础版所有功能</div>
                <div class="feature">✓ 批量操作</div>
                <div class="feature">✓ 导入导出</div>
                <div class="feature">✓ 自动备份</div>
                <div class="feature">✓ 自定义命令</div>
                <div class="feature">✓ 高级报告</div>
              </div>
            </div>
          </div>
        </div>



        <!-- 授权码获取指引 -->
        <div class="purchase-info">
          <h3>💼 正式授权码获取</h3>
          <div class="purchase-content">
            <div class="purchase-item">
              <strong>基础版授权码：</strong>
              <p>联系销售获取免费基础版授权码</p>
              <p>📧 邮箱：<EMAIL></p>
              <p>📞 电话：400-123-4567</p>
            </div>
            <div class="purchase-item">
              <strong>专业版授权码：</strong>
              <p>购买专业版获取完整功能授权码</p>
              <p>📧 邮箱：<EMAIL></p>
              <p>📞 电话：400-123-4568</p>
              <p>💰 价格：￥2999/年</p>
            </div>
          </div>
        </div>
      </div>

      <!-- 成功消息 -->
      <div v-if="successMessage" class="success-message">
        <span class="success-icon">✅</span>
        {{ successMessage }}
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { licenseManager } from '@/utils/license'

// Props
const props = defineProps({
  mode: {
    type: String,
    default: 'initial', // 'initial', 'upgrade', 'reauth'
    validator: (value) => ['initial', 'upgrade', 'reauth'].includes(value)
  },
  currentLicenseInfo: {
    type: Object,
    default: null
  },
  onLicenseActivated: {
    type: Function,
    required: true
  },
  onCancel: {
    type: Function,
    default: null
  }
})

// 响应式数据
const licenseCode = ref('')
const activating = ref(false)
const errorMessage = ref('')
const successMessage = ref('')

// 方法
const clearError = () => {
  errorMessage.value = ''
}



const activateLicense = async () => {
  if (!licenseCode.value.trim()) {
    errorMessage.value = '请输入授权码'
    return
  }

  activating.value = true
  errorMessage.value = ''
  successMessage.value = ''

  try {
    const result = licenseManager.activateLicense(licenseCode.value)

    if (result.success) {
      if (props.mode === 'upgrade') {
        successMessage.value = '升级成功！正在跳转...'
      } else if (props.mode === 'reauth') {
        successMessage.value = '授权更换成功！正在跳转...'
      } else {
        successMessage.value = result.message
      }

      setTimeout(() => {
        props.onLicenseActivated(result)
      }, 1000)
    } else {
      errorMessage.value = result.error || '授权激活失败'
    }
  } catch (error) {
    console.error('激活失败:', error)
    errorMessage.value = '激活过程中发生错误，请重试'
  } finally {
    activating.value = false
  }
}

const handleCancel = () => {
  if (props.onCancel) {
    props.onCancel()
  }
}
</script>

<style scoped>
.license-gate {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.gate-container {
  background: white;
  border-radius: 16px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  max-width: 900px;
  width: 100%;
  overflow: hidden;
}

.system-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 40px;
  text-align: center;
}

.system-logo {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.logo-icon {
  font-size: 48px;
  background: rgba(255, 255, 255, 0.2);
  width: 80px;
  height: 80px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.system-header h1 {
  margin: 0;
  font-size: 32px;
  font-weight: bold;
}

.system-subtitle {
  font-size: 16px;
  opacity: 0.9;
  margin-top: 8px;
}

.license-input-section {
  padding: 40px;
  border-bottom: 1px solid #f0f0f0;
}

.input-header {
  text-align: center;
  margin-bottom: 32px;
}

.input-header h2 {
  margin: 0 0 8px 0;
  color: #333;
  font-size: 24px;
}

.input-header p {
  margin: 0;
  color: #666;
  font-size: 16px;
}

.license-form {
  max-width: 400px;
  margin: 0 auto;
}

.current-license-info {
  margin: 20px 0;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #667eea;
}

.current-status h4 {
  margin: 0 0 12px 0;
  color: #333;
  font-size: 16px;
}

.status-details {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.status-item {
  font-size: 14px;
  color: #555;
}

.status-item strong {
  color: #333;
  margin-right: 8px;
}

.input-group {
  margin-bottom: 20px;
}

.input-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #333;
}

.license-input {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid #e1e5e9;
  border-radius: 8px;
  font-size: 16px;
  transition: border-color 0.2s;
}

.license-input:focus {
  outline: none;
  border-color: #667eea;
}

.button-group {
  display: flex;
  gap: 12px;
}

.activate-btn {
  flex: 1;
  padding: 14px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: transform 0.2s;
}

.activate-btn:hover:not(:disabled) {
  transform: translateY(-2px);
}

.activate-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.cancel-btn {
  padding: 14px 20px;
  background: #6c757d;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.cancel-btn:hover:not(:disabled) {
  background: #5a6268;
}

.cancel-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.error-message {
  margin-top: 16px;
  padding: 12px;
  background: #fff2f0;
  border: 1px solid #ffccc7;
  border-radius: 6px;
  color: #ff4d4f;
  display: flex;
  align-items: center;
  gap: 8px;
}

.success-message {
  margin-top: 16px;
  padding: 12px;
  background: #f6ffed;
  border: 1px solid #b7eb8f;
  border-radius: 6px;
  color: #52c41a;
  display: flex;
  align-items: center;
  gap: 8px;
}

.license-info {
  padding: 40px;
}

.info-section h3,
.demo-section h3,
.purchase-info h3 {
  margin: 0 0 20px 0;
  color: #333;
  font-size: 20px;
}

.license-types {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin-bottom: 40px;
}

.license-type {
  border: 2px solid #f0f0f0;
  border-radius: 12px;
  padding: 20px;
  transition: border-color 0.2s;
}

.license-type.professional {
  border-color: #667eea;
  background: linear-gradient(135deg, #f8f9ff 0%, #f0f2ff 100%);
}

.type-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.type-badge {
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 14px;
  font-weight: bold;
}

.type-badge.basic {
  background: #e8f4fd;
  color: #1890ff;
}

.type-badge.professional {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.type-price {
  font-size: 12px;
  color: #666;
}

.type-features {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.feature {
  font-size: 14px;
  color: #555;
}

.demo-codes {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin-bottom: 40px;
}

.demo-group h4 {
  margin: 0 0 12px 0;
  color: #555;
  font-size: 16px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.demo-group.highlighted {
  background: linear-gradient(135deg, #f8f9ff 0%, #f0f2ff 100%);
  border: 2px solid #667eea;
  border-radius: 12px;
  padding: 16px;
}

.demo-group.highlighted h4 {
  color: #667eea;
  font-weight: bold;
}

.recommended {
  background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
  color: white;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: bold;
}

.code-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px;
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  cursor: pointer;
  transition: background-color 0.2s;
  margin-bottom: 8px;
}

.code-item:hover {
  background: #e9ecef;
}

.code-item code {
  font-family: 'Courier New', monospace;
  font-size: 14px;
  color: #333;
}

.copy-hint {
  font-size: 12px;
  color: #666;
}

.purchase-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.purchase-item {
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #667eea;
}

.purchase-item strong {
  color: #333;
  font-size: 16px;
}

.purchase-item p {
  margin: 8px 0 4px 0;
  color: #555;
  font-size: 14px;
}

@media (max-width: 768px) {
  .license-types,
  .demo-codes,
  .purchase-content {
    grid-template-columns: 1fr;
  }
  
  .gate-container {
    margin: 10px;
  }
  
  .system-header,
  .license-input-section,
  .license-info {
    padding: 20px;
  }
}
</style>
