import { createRouter, createWebHistory } from 'vue-router'
import { createLicenseGuard, setupLicenseListeners } from './license-guard.js'

// 直接导入测试组件
import TestComponent from '../components/TestComponent.vue'

// 懒加载路由组件
const DevicesView = () => import('../views/DevicesView.vue')
const BackupsView = () => import('../views/BackupsView.vue')
const ComparisonView = () => import('../views/ComparisonView.vue')
const SettingsView = () => import('../views/SettingsView.vue')
const CommandsView = () => import('../views/CommandsView.vue')


// 增强的授权组件
const EnhancedLicenseGate = () => import('../components/EnhancedLicenseGate.vue')

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      redirect: '/devices'
    },
    {
      path: '/license-gate',
      name: 'license-gate',
      component: EnhancedLicenseGate,
      meta: {
        requiresAuth: false,
        title: '授权验证'
      }
    },
    {
      path: '/test',
      name: 'test',
      component: TestComponent,
      meta: { requiresAuth: false }
    },
    {
      path: '/devices',
      name: 'devices',
      component: DevicesView,
      meta: {
        requiresAuth: true,
        title: '设备管理'
      }
    },
    {
      path: '/backups',
      name: 'backups',
      component: BackupsView,
      meta: {
        requiresAuth: true,
        title: '配置备份'
      }
    },
    {
      path: '/device-backups/:id',
      name: 'deviceBackups',
      component: BackupsView,
      props: true,
      meta: {
        requiresAuth: true,
        title: '设备备份'
      }
    },
    {
      path: '/commands',
      name: 'commands',
      component: CommandsView,
      meta: {
        requiresAuth: true,
        title: '自定义命令'
      }
    },
    {
      path: '/comparison',
      name: 'comparison',
      component: ComparisonView,
      meta: {
        requiresAuth: true,
        title: '配置对比'
      }
    },
    {
      path: '/settings',
      name: 'settings',
      component: SettingsView,
      meta: {
        requiresAuth: true,
        title: '系统设置'
      }
    },

  ]
})

// 添加授权守卫
router.beforeEach(createLicenseGuard())

// 设置授权监听器
setupLicenseListeners(router)

export default router 