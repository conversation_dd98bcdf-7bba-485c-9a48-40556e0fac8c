#!/usr/bin/env python3
"""
授权系统数据库迁移脚本
用于创建和初始化授权相关的数据库表
"""

import os
import sys
from datetime import datetime
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database import Base, License, LicenseVerificationLog, init_db

def migrate_license_tables():
    """迁移授权相关的数据库表"""
    try:
        print("🚀 开始授权系统数据库迁移...")
        
        # 创建数据库目录
        db_dir = os.path.join(os.path.dirname(__file__), 'data')
        if not os.path.exists(db_dir):
            os.makedirs(db_dir)
            print(f"✅ 创建数据库目录: {db_dir}")
        
        # 创建数据库连接
        db_path = os.path.join(db_dir, 'netdevices.db')
        engine = create_engine(f'sqlite:///{db_path}', connect_args={"check_same_thread": False})
        
        print(f"📁 数据库路径: {db_path}")
        
        # 检查表是否存在
        with engine.connect() as conn:
            # 检查licenses表
            result = conn.execute(text("SELECT name FROM sqlite_master WHERE type='table' AND name='licenses'"))
            licenses_exists = result.fetchone() is not None
            
            # 检查license_verification_logs表
            result = conn.execute(text("SELECT name FROM sqlite_master WHERE type='table' AND name='license_verification_logs'"))
            logs_exists = result.fetchone() is not None
            
            print(f"📊 licenses表存在: {licenses_exists}")
            print(f"📊 license_verification_logs表存在: {logs_exists}")
        
        # 创建所有表
        print("🔨 创建数据库表...")
        Base.metadata.create_all(bind=engine)
        
        # 验证表创建
        with engine.connect() as conn:
            # 获取所有表名
            result = conn.execute(text("SELECT name FROM sqlite_master WHERE type='table'"))
            tables = [row[0] for row in result.fetchall()]
            
            print("📋 数据库中的表:")
            for table in sorted(tables):
                print(f"   • {table}")
            
            # 检查授权表结构
            if 'licenses' in tables:
                result = conn.execute(text("PRAGMA table_info(licenses)"))
                columns = result.fetchall()
                print("\n🔍 licenses表结构:")
                for col in columns:
                    print(f"   • {col[1]} ({col[2]})")
            
            if 'license_verification_logs' in tables:
                result = conn.execute(text("PRAGMA table_info(license_verification_logs)"))
                columns = result.fetchall()
                print("\n🔍 license_verification_logs表结构:")
                for col in columns:
                    print(f"   • {col[1]} ({col[2]})")
        
        print("\n✅ 授权系统数据库迁移完成！")
        return True
        
    except Exception as e:
        print(f"❌ 数据库迁移失败: {e}")
        return False

def test_license_operations():
    """测试授权相关的数据库操作"""
    try:
        print("\n🧪 测试授权数据库操作...")
        
        # 创建数据库连接
        db_dir = os.path.join(os.path.dirname(__file__), 'data')
        db_path = os.path.join(db_dir, 'netdevices.db')
        engine = create_engine(f'sqlite:///{db_path}', connect_args={"check_same_thread": False})
        SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
        
        with SessionLocal() as db:
            # 测试创建授权记录
            test_license = License(
                license_code="TEST-2024-ABCD-EFGH",
                license_type="professional",
                device_fingerprint="test_fingerprint_12345",
                activated_at=datetime.utcnow(),
                is_active=True,
                verification_count=1
            )
            
            db.add(test_license)
            db.commit()
            db.refresh(test_license)
            
            print(f"✅ 创建测试授权记录: ID={test_license.id}")
            
            # 测试创建验证日志
            test_log = LicenseVerificationLog(
                license_id=test_license.id,
                device_fingerprint="test_fingerprint_12345",
                verification_result="success",
                verified_at=datetime.utcnow()
            )
            
            db.add(test_log)
            db.commit()
            db.refresh(test_log)
            
            print(f"✅ 创建测试验证日志: ID={test_log.id}")
            
            # 查询测试
            license_count = db.query(License).count()
            log_count = db.query(LicenseVerificationLog).count()
            
            print(f"📊 授权记录总数: {license_count}")
            print(f"📊 验证日志总数: {log_count}")
            
            # 清理测试数据
            db.delete(test_log)
            db.delete(test_license)
            db.commit()
            
            print("🧹 清理测试数据完成")
        
        print("✅ 授权数据库操作测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 授权数据库操作测试失败: {e}")
        return False

def show_license_statistics():
    """显示授权统计信息"""
    try:
        print("\n📊 授权系统统计信息...")
        
        # 创建数据库连接
        db_dir = os.path.join(os.path.dirname(__file__), 'data')
        db_path = os.path.join(db_dir, 'netdevices.db')
        engine = create_engine(f'sqlite:///{db_path}', connect_args={"check_same_thread": False})
        SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
        
        with SessionLocal() as db:
            # 统计授权记录
            total_licenses = db.query(License).count()
            active_licenses = db.query(License).filter(License.is_active == True).count()
            basic_licenses = db.query(License).filter(License.license_type == 'basic').count()
            professional_licenses = db.query(License).filter(License.license_type == 'professional').count()
            
            # 统计验证日志
            total_verifications = db.query(LicenseVerificationLog).count()
            successful_verifications = db.query(LicenseVerificationLog).filter(
                LicenseVerificationLog.verification_result == 'success'
            ).count()
            
            print(f"📈 总授权数: {total_licenses}")
            print(f"📈 活跃授权数: {active_licenses}")
            print(f"📈 基础版授权数: {basic_licenses}")
            print(f"📈 专业版授权数: {professional_licenses}")
            print(f"📈 总验证次数: {total_verifications}")
            print(f"📈 成功验证次数: {successful_verifications}")
            
            if total_verifications > 0:
                success_rate = (successful_verifications / total_verifications) * 100
                print(f"📈 验证成功率: {success_rate:.1f}%")
        
        return True
        
    except Exception as e:
        print(f"❌ 获取统计信息失败: {e}")
        return False

def main():
    """主函数"""
    print("🔐 网络设备备份工具 - 授权系统数据库迁移")
    print("=" * 60)
    
    # 执行迁移
    if not migrate_license_tables():
        sys.exit(1)
    
    # 测试操作
    if not test_license_operations():
        sys.exit(1)
    
    # 显示统计信息
    show_license_statistics()
    
    print("\n🎉 授权系统初始化完成！")
    print("\n💡 使用说明:")
    print("   1. 启动后端服务: python main.py")
    print("   2. 启动前端服务: npm run dev")
    print("   3. 访问应用并进行授权验证")


if __name__ == "__main__":
    main()
