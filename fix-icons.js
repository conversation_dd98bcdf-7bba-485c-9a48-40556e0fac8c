#!/usr/bin/env node

/**
 * 修复 Arco Design 图标导入问题
 */

const fs = require('fs');
const path = require('path');

// 需要修复的文件列表
const filesToFix = [
  'frontend/src/App.vue',
  'frontend/src/components/EnhancedLicenseGate.vue'
];

// 图标映射 - 将不存在的图标替换为存在的图标
const iconMappings = {
  'IconShieldCheck': 'IconCheckCircle',
  'IconExclamationCircle': 'IconCloseCircle',
  'IconSafe': 'IconCheckCircle',
  'IconExclamationCircleFill': 'IconExclamationCircleFill',
  'icon-shield-check': 'icon-check-circle',
  'icon-exclamation-circle': 'icon-close-circle',
  'icon-safe': 'icon-check-circle'
};

function fixIconsInFile(filePath) {
  try {
    console.log(`🔧 修复文件: ${filePath}`);
    
    if (!fs.existsSync(filePath)) {
      console.log(`⚠️  文件不存在: ${filePath}`);
      return false;
    }
    
    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;
    
    // 替换图标名称
    for (const [oldIcon, newIcon] of Object.entries(iconMappings)) {
      const regex = new RegExp(oldIcon, 'g');
      if (content.includes(oldIcon)) {
        content = content.replace(regex, newIcon);
        modified = true;
        console.log(`   ✅ 替换 ${oldIcon} -> ${newIcon}`);
      }
    }
    
    if (modified) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`   💾 文件已保存`);
      return true;
    } else {
      console.log(`   ℹ️  无需修改`);
      return false;
    }
    
  } catch (error) {
    console.error(`❌ 修复文件失败 ${filePath}:`, error.message);
    return false;
  }
}

function main() {
  console.log('🚀 开始修复 Arco Design 图标问题...');
  console.log('='.repeat(50));
  
  let totalFixed = 0;
  
  for (const filePath of filesToFix) {
    if (fixIconsInFile(filePath)) {
      totalFixed++;
    }
  }
  
  console.log('='.repeat(50));
  console.log(`🎉 修复完成！共修复 ${totalFixed} 个文件`);
  
  if (totalFixed > 0) {
    console.log('\n💡 建议操作:');
    console.log('   1. 刷新浏览器页面');
    console.log('   2. 检查控制台是否还有错误');
    console.log('   3. 测试应用功能是否正常');
  }
}

if (require.main === module) {
  main();
}
